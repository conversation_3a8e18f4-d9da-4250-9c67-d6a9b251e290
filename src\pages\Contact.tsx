import { useEffect } from "react";
import MetaTags from "@/components/MetaTags";
import Contact from "@/components/Contact";

const ContactPage = () => {
  useEffect(() => {
    document.title = "Contact - The Brown Patience Company";
  }, []);

  return (
    <>
      <MetaTags
        title="Contact Brown Patience - Get in Touch | Writing Coach & Author"
        description="Connect with <PERSON> Patience for writing coaching, book consultations, or speaking engagements. Get personalized guidance for your writing journey."
        image="/og-images/home.webp"
        url="https://thebrownpatiencecompany.com.ng/contact"
        keywords="contact <PERSON> Patience, writing coach contact, book consultation, speaking engagements, writing guidance"
      />
      <div className="min-h-screen bg-cream-50 pt-16">
        <Contact
          title="Let's Connect"
          subtitle="Get in Touch"
          description="Whether you're seeking guidance on your writing journey, need support with mental transformation, or have questions about my books and services, I'm here to help. Reach out and let's start a conversation."
          whatsappMessage="Hi! I'd love to connect and learn more about your services. Could we schedule a time to chat?"
          showPhone={true}
          showEmail={true}
          phoneNumber="+234 (0) ************"
          email="<EMAIL>"
          backgroundColor="bg-cream-50"
        />
      </div>
    </>
  );
};

export default ContactPage;
