import { useEffect } from "react";
import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import MetaTags from "@/components/MetaTags";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
  Droplets,
  BookOpen,
  Heart,
  ArrowRight,
  ExternalLink,
  Sparkles,
  Instagram,
} from "lucide-react";
import { useTypewriter } from "@/hooks/useTypewriter";
import OasisBg from "@/assets/cover/oasisBg.webp";
import OasisLogo from "@/assets/cover/oasisLogo.webp";
import soHeTaughtMeNo from "@/assets/cover/soHeTaughtMeNo.webp";
import fantasy from "@/assets/cover/fantasy.webp";
import chooseYourThoughts from "@/assets/cover/chooseYourThoughts.webp";
import { Link } from "react-router-dom";

const Community = () => {
  const [heroRef, heroInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const [storyRef, storyInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const [booksRef, booksInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  useEffect(() => {
    document.title = "Welcome To Oasis - The Brown Patience Company";
  }, []);

  const heroSpring = useSpring({
    opacity: heroInView ? 1 : 0,
    transform: heroInView ? "translateY(0px)" : "translateY(50px)",
  });

  const storySpring = useSpring({
    opacity: storyInView ? 1 : 0,
    transform: storyInView ? "translateY(0px)" : "translateY(50px)",
  });

  const booksSpring = useSpring({
    opacity: booksInView ? 1 : 0,
    transform: booksInView ? "translateY(0px)" : "translateY(50px)",
  });

  // Typewriter animation for the hero header
  const typewriterText = useTypewriter("Welcome To Oasis", {
    speed: 100,
    delay: 2000,
    infinite: true,
  });

  const recommendedBooks = [
    {
      id: 1,
      title: "So He Taught Me 'No'",
      author: "Brown Patience",
      description:
        "This is a book about porn addiction. About the darkness that persists even after you've stopped viewing it. All based on true experience...",
      coverUrl: soHeTaughtMeNo,
      link: "https://selar.co/SHTM-NO", // Replace with actual link
    },
    {
      id: 2,
      title: "Choose Your Thoughts",
      author: "Brown Patience",
      description:
        "What you do with your mind will turn and do you. I should know. If you brood and ruminate on depressing thoughts...",
      coverUrl: chooseYourThoughts,
      link: "https://selar.co/ChooseYourThoughts", // Replace with actual link
    },
    {
      id: 3,
      title: "Fantasy",
      author: "Brown Patience",
      description:
        "Fantasy is about the sexual mental struggles we face. Those steamy thoughts that feel like you're engaging in a porn video production...",
      coverUrl: fantasy,
      link: "https://selar.co/FANTASY", // Replace with actual link
    },
  ];

  const handleBookClick = (url: string) => {
    window.open(url, "_blank", "noopener,noreferrer");
  };

  return (
    <>
      <MetaTags
        title="Oasis Community - Brown Patience | Join Our Writing Community"
        description="Join the Oasis Community - a supportive space for writers to grow, connect, and find inspiration. Discover resources, books, and fellowship for your writing journey."
        image="/og-images/community.webp"
        url="https://thebrownpatiencecompany.com.ng/community"
        keywords="writing community, Oasis community, writer support, writing fellowship, Brown Patience community"
      />
      <div className="min-h-screen bg-cream-50 ">
        {/* Hero Section */}
        <section
          ref={heroRef}
          className="py-20 oasis-bg relative overflow-hidden"
          style={{
            backgroundImage: `linear-gradient(135deg, rgba(120, 180, 160, 0.9), rgba(80, 160, 140, 0.9)), url(${OasisBg})`,
            backgroundSize: "cover",
            backgroundPosition: "center",
          }}
        >
          <div className="container mx-auto px-4">
            <animated.div style={heroSpring} className="max-w-6xl mx-auto">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                {/* Text Content */}
                <div className="text-center lg:text-left">
                  <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm font-medium mb-6">
                    <Droplets className="w-4 h-4" />
                    Oasis
                  </div>
                  <h1 className="text-4xl md:text-6xl font-serif font-bold text-white mb-6">
                    {typewriterText}
                    <span className="typewriter-cursor"></span>
                  </h1>
                  <p className="text-xl text-white/90 mb-8 leading-relaxed">
                    You don't like what happens in your thoughts. You'd like to
                    change that. This is an Oasis of Purifying Water &
                    directions for use.
                  </p>
                  <Link to="https://www.instagram.com/theoasis.network?igsh=aWJ6YnhoazB6bDIy">
                    <Button
                      size="lg"
                      className="bg-white hover:bg-brown-900 text-black px-8 py-3"
                    >
                      <Instagram className="w-5 h-5 mr-2" />
                      Learn More
                    </Button>
                  </Link>
                </div>

                {/* Hero Image */}
                <div className="flex justify-center lg:justify-end">
                  <div className="relative">
                    <img
                      src={OasisLogo}
                      alt="Oasis - Mind Renewal Center"
                      className="w-80 h-80 rounded-full shadow-2xl bg-white/10 backdrop-blur-sm p-8 hover:scale-105 transition-transform duration-300"
                      loading="eager"
                    />
                    {/* Floating elements for visual interest */}
                    <div className="absolute -top-4 -right-4 w-20 h-20 bg-white/10 rounded-full blur-xl animate-pulse"></div>
                    <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-white/5 rounded-full blur-2xl animate-pulse delay-1000"></div>
                  </div>
                </div>
              </div>
            </animated.div>
          </div>
        </section>

        {/* How The Oasis Came About */}
        <section
          ref={storyRef}
          className="py-20 bg-gradient-to-br from-green-50 to-teal-50"
        >
          <div className="container mx-auto px-4">
            <animated.div style={storySpring}>
              <div className="max-w-6xl mx-auto">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-6">
                    How The Oasis Came About
                  </h2>
                </div>

                <div className="grid lg:grid-cols-2 gap-12 items-start mb-8">
                  {/* Story Content */}
                  <div className="prose prose-lg max-w-none text-brand-secondary/80 leading-relaxed space-y-6">
                    <p>
                      Pornography found me early in primary school. Primary
                      school is that time when you're around 10 years old. Yeah,
                      that's when pornography found me. Or maybe I found it. And
                      it held me tight till I turned to Jesus at 15. But turning
                      to Jesus didn't give me a mind wipe. All the porn stored
                      in my mind, my memory, did not disappear. So though I
                      stopped watching more porn videos, my mind still had its
                      memories, its archive. It still knew how to create its own
                      series, to make its own fantasies. Even though I was now a
                      Believer.
                    </p>

                    <p>
                      I didn't know how to stop them, how to quit thinking
                      dirty, pulse-increasing thoughts! And I was desperate to
                      be free. "Oh God, will I think these kinds of thoughts
                      forever?"
                    </p>
                    <p>
                      Hell no! He taught me to tell them "No!" It's what His
                      Word said He'd do in Titus 2:11 & 12 — literally, word for
                      word! "For the grace of God has appeared that offers
                      salvation to all people. It TEACHES us to say 'No' to
                      ungodliness and worldly passions, and to live
                      self-controlled, upright and godly lives in this present
                      age. . ."
                    </p>
                  </div>

                  {/* Story Image */}
                  <div className="flex justify-center">
                    <div className="relative">
                      <img
                        src={OasisBg}
                        alt="Oasis - A place of renewal and restoration"
                        className="w-full h-96 rounded-2xl object-cover shadow-elegant"
                        loading="lazy"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl"></div>
                      <div className="absolute bottom-4 left-4 right-4">
                        <p className="text-white font-medium text-sm bg-black/50 backdrop-blur-sm rounded-lg px-3 py-2">
                          "God's Word is an Oasis. It refreshes. It washes
                          clean."
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex flex-col items-center justify-center ">
                  <div className="prose prose-lg max-w-none text-brand-secondary/80 leading-relaxed space-y-6">
                    <p>
                      That's precisely what the Holy Spirit taught me to do — to
                      tell ungodly thoughts "No!" to take control of my
                      thoughts, to have my mind renewed by His Word. And He can
                      teach you too! Oh, He can make you free. So free that
                      you'd struggle to remember what used to be compulsions.
                    </p>
                    <p>
                      Totally without planning to, I chronicled my experience in
                      the book So He Taught Me 'No'. "Weren't you ashamed to
                      write about these things?" Not one bit! Cause it's so much
                      more important to have my freedom replicated in the lives
                      of my brothers and sisters in Christ than to present a
                      perfect image of myself.
                    </p>
                    <p className="text-xl font-semibold text-brand-secondary">
                      And that's how the Oasis came about.
                    </p>
                    <p>
                      An Oasis, by definition, is a body of water in a desert.
                      God's Word is an Oasis. It refreshes. It washes clean. No
                      matter the dirt our minds have been covered in, no matter
                      the level of vile in our thoughts, no matter the depth of
                      our addictions, God's Word can wash that mind so clean,
                      make it so new that its past becomes a fading memory.
                    </p>
                    <p className="text-lg font-medium text-teal-600">
                      Oasis is a mind renewal centre. It focuses on thoughts,
                      the mind, and the God who can renew. You can download
                      these mind books for free:
                    </p>
                  </div>
                </div>
              </div>
            </animated.div>
          </div>
        </section>

        {/* Recommended Books */}
        <section
          ref={booksRef}
          className="py-20 bg-gradient-to-br from-teal-100/50 to-green-100/50"
        >
          <div className="container mx-auto px-4">
            <animated.div style={booksSpring}>
              <div className="text-center mb-16">
                <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
                  Recommended Books
                </h2>
                <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
                  Mind renewal books to help you on your journey to freedom and
                  mental transformation.
                </p>
              </div>

              <div className="grid md:grid-cols-3 gap-8">
                {recommendedBooks.map((book, index) => (
                  <animated.div
                    key={book.id}
                    style={{
                      opacity: booksInView ? 1 : 0,
                      transform: booksInView
                        ? "translateY(0px)"
                        : "translateY(50px)",
                      transitionDelay: booksInView
                        ? `${200 + index * 100}ms`
                        : "0ms",
                      transition: "all 0.6s ease-out",
                    }}
                  >
                    <Card className="h-full hover:shadow-lg transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1 group">
                      <div className="aspect-[3/4] overflow-hidden rounded-t-lg">
                        <img
                          src={book.coverUrl}
                          alt={`${book.title} cover`}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                          loading="lazy"
                        />
                      </div>
                      <CardHeader>
                        <h3 className="text-lg font-serif font-bold text-brand-secondary mb-2 group-hover:text-teal-600 transition-colors">
                          {book.title}
                        </h3>
                        <p className="text-sm font-medium text-teal-600 mb-2">
                          {book.author}
                        </p>
                        <p className="text-brand-secondary/70 text-sm leading-relaxed">
                          {book.description}
                        </p>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <Button
                          onClick={() => handleBookClick(book.link)}
                          variant="outline"
                          className="w-full group/btn border-teal-500 text-teal-600 hover:bg-teal-500 hover:text-white"
                        >
                          Buy Now
                          <ExternalLink className="w-4 h-4 ml-2 group-hover/btn:translate-x-1 transition-transform" />
                        </Button>
                      </CardContent>
                    </Card>
                  </animated.div>
                ))}
              </div>
            </animated.div>
          </div>
        </section>
      </div>
    </>
  );
};

export default Community;
