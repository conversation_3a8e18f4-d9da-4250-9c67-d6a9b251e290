import { useState } from "react";
import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Mail, Gift, CheckCircle, AlertCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const Newsletter = () => {
  const [email, setEmail] = useState("");
  const [name, setName] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const { toast } = useToast();

  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const sectionSpring = useSpring({
    opacity: inView ? 1 : 0,
    transform: inView ? "translateY(0px)" : "translateY(50px)",
  });

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateEmail(email)) {
      toast({
        title: "Invalid email",
        description: "Please enter a valid email address.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulated API call - replace with actual Supabase integration
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // In a real implementation, this would call your Supabase function:
      // const response = await fetch('/api/subscribe', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({
      //     email,
      //     name: name || undefined,
      //     source: 'homepage',
      //     utm: window.location.search,
      //   }),
      // });

      console.log("Newsletter signup:", { email, name, source: "homepage" });

      setIsSubmitted(true);
      toast({
        title: "Welcome aboard! 🎉",
        description: "Check your inbox for your free chapter and writing tips.",
      });

      // Reset form after success
      setTimeout(() => {
        setEmail("");
        setName("");
        setIsSubmitted(false);
      }, 3000);
    } catch (error) {
      toast({
        title: "Something went wrong",
        description: "Please try again later or contact support.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section id="newsletter" ref={ref} className="py-20 bg-brand-accent/5">
      <div className="container mx-auto px-4">
        <animated.div style={sectionSpring}>
          <div className="max-w-4xl mx-auto">
            <Card className="shadow-elegant border-0 bg-brand-primary/95 backdrop-blur-sm">
              <CardContent className="p-8 md:p-12">
                <div className="text-center mb-8">
                  <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium mb-4">
                    <Gift className="w-4 h-4" />
                    Free Resources
                  </div>

                  <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
                    Get a Free Chapter + Weekly Writing Tips
                  </h2>

                  <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
                    Join 2,000+ writers who receive practical insights,
                    publishing updates, and encouragement delivered to their
                    inbox every week.
                  </p>
                </div>

                {!isSubmitted ? (
                  <form
                    onSubmit={handleSubmit}
                    className="space-y-4 max-w-md mx-auto"
                  >
                    <div className="space-y-2">
                      <Label
                        htmlFor="newsletter-name"
                        className="text-brand-secondary font-medium"
                      >
                        First Name (optional)
                      </Label>
                      <Input
                        id="newsletter-name"
                        type="text"
                        placeholder="Your name"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        className="h-12 text-base"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label
                        htmlFor="newsletter-email"
                        className="text-brand-secondary font-medium"
                      >
                        Email Address *
                      </Label>
                      <Input
                        id="newsletter-email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        required
                        className="h-12 text-base"
                      />
                    </div>

                    <Button
                      type="submit"
                      variant="hero"
                      size="lg"
                      disabled={isSubmitting || !email}
                      className="w-full h-12 text-base font-semibold"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="w-4 h-4 border-2 border-primary-foreground/20 border-t-primary-foreground rounded-full animate-spin mr-2" />
                          Joining...
                        </>
                      ) : (
                        <>
                          <Mail className="w-5 h-5 mr-2" />
                          Join the Community
                        </>
                      )}
                    </Button>

                    <p className="text-xs text-brand-secondary/60 text-center leading-relaxed">
                      No spam, ever. Unsubscribe anytime. By subscribing, you
                      agree to our privacy policy.
                    </p>
                  </form>
                ) : (
                  <div className="text-center max-w-md mx-auto">
                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <CheckCircle className="w-8 h-8 text-green-600" />
                    </div>
                    <h3 className="text-xl font-semibold text-brand-secondary mb-2">
                      Welcome to the community!
                    </h3>
                    <p className="text-brand-secondary/70">
                      Check your inbox for your free chapter and first writing
                      tip.
                    </p>
                  </div>
                )}

                {/* Benefits */}
                <div className="grid md:grid-cols-3 gap-6 mt-12 pt-8 border-t border-brand-grey/20">
                  <div className="text-center">
                    <div className="w-12 h-12 bg-brand-accent/10 rounded-full flex items-center justify-center mx-auto mb-3">
                      <Gift className="w-6 h-6 text-brand-accent" />
                    </div>
                    <h4 className="font-semibold text-brand-secondary mb-1">
                      Free Resources
                    </h4>
                    <p className="text-sm text-brand-secondary/60">
                      Writing guides, templates, and tools
                    </p>
                  </div>

                  <div className="text-center">
                    <div className="w-12 h-12 bg-brand-accent/10 rounded-full flex items-center justify-center mx-auto mb-3">
                      <Mail className="w-6 h-6 text-brand-accent" />
                    </div>
                    <h4 className="font-semibold text-brand-secondary mb-1">
                      Weekly Tips
                    </h4>
                    <p className="text-sm text-brand-secondary/60">
                      Practical advice for your writing journey
                    </p>
                  </div>

                  <div className="text-center">
                    <div className="w-12 h-12 bg-brand-accent/10 rounded-full flex items-center justify-center mx-auto mb-3">
                      <CheckCircle className="w-6 h-6 text-brand-accent" />
                    </div>
                    <h4 className="font-semibold text-brand-secondary mb-1">
                      Early Access
                    </h4>
                    <p className="text-sm text-brand-secondary/60">
                      First to know about new books and courses
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </animated.div>
      </div>
    </section>
  );
};

export default Newsletter;
