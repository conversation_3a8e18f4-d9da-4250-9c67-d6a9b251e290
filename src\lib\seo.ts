// Utility functions for SEO and Open Graph meta tags
export interface SEOConfig {
  title: string;
  description: string;
  image: string;
  url: string;
  type?: string;
}

/**
 * Updates the Open Graph and Twitter meta tags dynamically
 * @param config SEO configuration object
 */
export function updateMetaTags(config: SEOConfig) {
  // Update basic meta tags
  updateMetaTag("description", config.description);
  updateMetaTag("keywords", config.title);

  // Update Open Graph meta tags
  updateMetaTag("og:title", config.title);
  updateMetaTag("og:description", config.description);
  updateMetaTag("og:image", config.image);
  updateMetaTag("og:url", config.url);
  updateMetaTag("og:type", config.type || "website");

  // Update Twitter meta tags
  updateMetaTag("twitter:title", config.title);
  updateMetaTag("twitter:description", config.description);
  updateMetaTag("twitter:image", config.image);
  updateMetaTag("twitter:card", "summary_large_image");
}

/**
 * Helper function to update or create a meta tag
 * @param property The meta property name
 * @param content The content value
 */
function updateMetaTag(property: string, content: string) {
  // Try to find existing meta tag
  let metaTag =
    document.querySelector(`meta[property="${property}"]`) ||
    document.querySelector(`meta[name="${property}"]`);

  if (metaTag) {
    // Update existing meta tag
    metaTag.setAttribute("content", content);
  } else {
    // Create new meta tag
    metaTag = document.createElement("meta");
    if (property.startsWith("og:")) {
      metaTag.setAttribute("property", property);
    } else {
      metaTag.setAttribute("name", property);
    }
    metaTag.setAttribute("content", content);
    document.head.appendChild(metaTag);
  }
}

/**
 * Default SEO configuration
 */
export const defaultSEOConfig: SEOConfig = {
  title: "The Brown Patience Company",
  description:
    "Professional writing coaching, editorial services, and published books to help writers find their voice and publish with confidence. Join 200+ successful authors.",
  image: "https://thebrownpatiencecompany.com.ng/og-images/default.jpg",
  url: "https://thebrownpatiencecompany.com.ng",
  type: "website",
};
