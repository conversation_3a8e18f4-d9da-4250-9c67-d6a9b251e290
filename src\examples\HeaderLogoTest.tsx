import React from 'react';
import Logo from "@/assets/logo.webp";

/**
 * Test component to demonstrate the logo visibility fix
 * This shows how the responsive classes work across different breakpoints
 */
const HeaderLogoTest = () => {
  return (
    <div className="p-8 space-y-8">
      <h1 className="text-2xl font-bold text-brand-secondary">
        Header Logo Responsive Test
      </h1>
      
      <div className="space-y-6">
        <div className="border border-brand-grey rounded-lg p-4">
          <h2 className="text-lg font-semibold mb-4">Fixed Logo Implementation</h2>
          
          {/* This is the FIXED version */}
          <div className="flex items-center gap-2 bg-white p-4 rounded border">
            {/* Desktop/Tablet Logo - visible from sm (640px) and up */}
            <img
              src={Logo}
              alt="Author Logo"
              className="hidden sm:block h-10 w-auto border border-green-500"
              title="Desktop/Tablet Logo (≥640px)"
            />

            {/* Mobile Logo - visible only below sm (640px) */}
            <img
              src={Logo}
              alt="Author Logo"
              className="block sm:hidden h-8 w-auto border border-blue-500"
              title="Mobile Logo (<640px)"
            />
            
            <span className="ml-4 text-sm text-brand-secondary">
              Current logo visibility (resize window to test)
            </span>
          </div>
        </div>

        <div className="border border-red-300 rounded-lg p-4">
          <h2 className="text-lg font-semibold mb-4 text-red-600">
            Broken Implementation (Before Fix)
          </h2>
          
          {/* This shows the BROKEN version for comparison */}
          <div className="flex items-center gap-2 bg-red-50 p-4 rounded border">
            {/* Old Desktop Logo - had gap issue */}
            <img
              src={Logo}
              alt="Author Logo"
              className="hidden md:block h-10 w-auto border border-red-500"
              title="Old Desktop Logo (≥768px) - CAUSED GAP"
            />

            {/* Old Mobile Logo */}
            <img
              src={Logo}
              alt="Author Logo"
              className="sm:hidden h-8 w-auto border border-red-500"
              title="Old Mobile Logo (<640px)"
            />
            
            <span className="ml-4 text-sm text-red-600">
              Broken version (logo disappears 640px-768px)
            </span>
          </div>
        </div>

        <div className="bg-brand-neutral/30 rounded-lg p-4">
          <h3 className="font-semibold mb-2">Breakpoint Reference:</h3>
          <ul className="text-sm space-y-1 text-brand-secondary/80">
            <li><strong>Mobile:</strong> &lt; 640px (sm) - Shows small logo (h-8)</li>
            <li><strong>Tablet/Medium:</strong> 640px - 1020px - Shows large logo (h-10)</li>
            <li><strong>Desktop:</strong> &gt; 1020px - Shows large logo (h-10)</li>
          </ul>
        </div>

        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="font-semibold text-green-800 mb-2">✅ Fix Summary:</h3>
          <ul className="text-sm space-y-1 text-green-700">
            <li>• Changed desktop logo from <code>hidden md:block</code> to <code>hidden sm:block</code></li>
            <li>• Made mobile logo explicit with <code>block sm:hidden</code></li>
            <li>• Eliminated 640px-768px visibility gap</li>
            <li>• Logo now visible across all screen sizes</li>
          </ul>
        </div>
      </div>

      <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="font-semibold text-blue-800 mb-2">🧪 Testing Instructions:</h3>
        <ol className="text-sm space-y-1 text-blue-700 list-decimal list-inside">
          <li>Open browser developer tools</li>
          <li>Toggle device toolbar (responsive mode)</li>
          <li>Test these widths: 320px, 640px, 768px, 1024px, 1280px</li>
          <li>Verify logo is always visible in the green-bordered section</li>
          <li>Notice the red-bordered section shows the gap issue</li>
        </ol>
      </div>
    </div>
  );
};

export default HeaderLogoTest;

/**
 * USAGE:
 * 
 * To test this component, add it to a route or import it in a page:
 * 
 * import HeaderLogoTest from '@/examples/HeaderLogoTest';
 * 
 * // In your component
 * <HeaderLogoTest />
 * 
 * Then use browser dev tools to test responsive behavior.
 */
