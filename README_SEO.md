# SEO and Open Graph Implementation Guide

This guide explains how to implement custom Open Graph images and meta tags for sharing links to your website.

## How It Works

The implementation uses a custom React hook (`useSEO`) that dynamically updates the page's meta tags when a component mounts. This allows each page to have its own unique title, description, and social sharing image.

## Implementation Steps

### 1. Create a Custom OG Image

1. Create or select an image for your page (recommended size: 1200x630 pixels)
2. Save it in the `public/og-images/` directory with a descriptive name
3. The image will be accessible at `https://yourdomain.com/og-images/filename.jpg`

### 2. Import and Use the SEO Hook

In your page component, add:

```typescript
import { useSEO } from "@/hooks/useSEO";

const YourPageComponent = () => {
  useSEO({
    title: "Your Page Title | Your Site Name",
    description: "A compelling description of your page content",
    image: "https://yourdomain.com/og-images/your-image.jpg",
    url: "https://yourdomain.com/your-page-path",
    type: "website", // or "article", "profile", etc.
  });

  // ... rest of your component
};
```

### 3. Required Properties

- `title`: The page title (will also appear in social shares)
- `description`: A concise description of the page content
- `image`: Full URL to your OG image
- `url`: Canonical URL of the page
- `type`: Open Graph type (optional, defaults to "website")

## Example Implementation

```typescript
import { useSEO } from "@/hooks/useSEO";

const ServicePage = () => {
  useSEO({
    title: "Professional Writing Services | The Brown Patience Company",
    description: "Expert writing coaching, editing, and publishing services for authors and content creators.",
    image: "https://thebrownpatiencecompany.com.ng/og-images/writing-services.jpg",
    url: "https://thebrownpatiencecompany.com.ng/services/writing",
    type: "website"
  });

  return (
    // ... your page content
  );
};
```

## Best Practices

1. **Image Size**: Use 1200x630 pixels for optimal display on social platforms
2. **File Format**: JPEG or PNG format works best
3. **Descriptive Names**: Use clear, descriptive filenames for your OG images
4. **Unique Per Page**: Each page should have its own unique OG image
5. **Text Safety**: Keep important text within the center 600x300px area to account for cropping

## Adding to New Pages

To add SEO functionality to any new page:

1. Create your OG image and place it in `public/og-images/`
2. Import the `useSEO` hook at the top of your component file
3. Call `useSEO()` with your page's specific configuration
4. That's it! Your page will now have custom social sharing metadata

## Troubleshooting

If your OG images aren't appearing:

1. Verify the image URL is accessible in a browser
2. Check that you're using the full URL (including https://)
3. Use Facebook's Sharing Debugger or Twitter's Card Validator to test
4. Ensure your page is deployed and the image is accessible publicly

## Updating Default Meta Tags

To update the default meta tags for the entire site, modify the values in `src/lib/seo.ts` in the `defaultSEOConfig` object.
