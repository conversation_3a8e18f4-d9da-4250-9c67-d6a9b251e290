import { useState } from "react";
import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>ronLef<PERSON>, Ch<PERSON>ronR<PERSON>, <PERSON>uo<PERSON>, <PERSON> } from "lucide-react";
import gTD from "@/data/generalTstmData";
import profileImg from "@/assets/profile.webp";

interface Testimonial {
  id: string;
  quote: string;
  author: string;
  role: string;
  rating: number;
  image: string;
}

// Transform generalTstmData to match our testimonial interface
const testimonials: Testimonial[] = gTD.map((item) => ({
  id: item.id.toString(),
  quote: item.content,
  author: item.name,
  role: "Reader",
  rating: 5,
  image: profileImg,
}));

const Testimonials = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const sectionSpring = useSpring({
    opacity: inView ? 1 : 0,
    transform: inView ? "translateY(0px)" : "translateY(50px)",
  });

  const slideSpring = useSpring({
    transform: `translateX(-${currentIndex * 100}%)`,
    config: { tension: 200, friction: 25 },
  });

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length);
  };

  const prevSlide = () => {
    setCurrentIndex(
      (prev) => (prev - 1 + testimonials.length) % testimonials.length
    );
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  return (
    <section ref={ref} className="py-20 bg-brand-neutral/30">
      <div className="container mx-auto px-4">
        <animated.div style={sectionSpring}>
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium mb-4">
              <Quote className="w-4 h-4" />
              Reader Testimonials
            </div>
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
              Writing Can Save Lives
            </h2>
            <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
              If you're wondering why you should go ahead and write, here's your
              writing fire! Books can save lives. These are real people
              testifying of real impact.
            </p>
          </div>

          {/* Testimonials Carousel */}
          <div className="max-w-4xl mx-auto">
            <div className="relative overflow-hidden rounded-2xl min-h-[400px] flex items-center">
              <animated.div style={slideSpring} className="flex w-full">
                {testimonials.map((testimonial) => (
                  <div
                    key={testimonial.id}
                    className="w-full flex-shrink-0 flex items-center justify-center"
                  >
                    <Card className="mx-4 shadow-elegant border-0 bg-brand-primary/95 w-full max-w-3xl">
                      <CardContent className="p-8 md:p-12 text-center flex flex-col items-center justify-center min-h-[320px]">
                        {/* Quote Icon */}
                        <div className="w-16 h-16 bg-brand-accent/10 rounded-full flex items-center justify-center mx-auto mb-6">
                          <Quote className="w-8 h-8 text-brand-accent" />
                        </div>

                        {/* Stars */}
                        <div className="flex justify-center gap-1 mb-6">
                          {[...Array(testimonial.rating)].map((_, i) => (
                            <Star
                              key={i}
                              className="w-5 h-5 fill-brand-accent text-brand-accent"
                            />
                          ))}
                        </div>

                        {/* Quote */}
                        <div className="flex-1 flex items-center justify-center">
                          <blockquote className="text-lg md:text-xl text-brand-secondary/90 leading-relaxed mb-8 italic font-serif max-w-2xl">
                            "{testimonial.quote}"
                          </blockquote>
                        </div>

                        {/* Author */}
                        <div className="flex flex-col items-center mt-auto">
                          <div className="w-16 h-16 rounded-full overflow-hidden mb-4">
                            <img
                              src={testimonial.image}
                              alt={testimonial.author}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <div className="font-semibold text-brand-secondary text-lg">
                            {testimonial.author}
                          </div>
                          <div className="text-brand-secondary/60 text-sm">
                            {testimonial.role}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                ))}
              </animated.div>
            </div>

            {/* Navigation */}
            <div className="flex items-center justify-center gap-4 mt-8">
              <Button
                variant="ghost"
                size="icon"
                onClick={prevSlide}
                className="rounded-full hover:bg-brand-accent/10"
              >
                <ChevronLeft className="w-5 h-5" />
              </Button>

              {/* Dots */}
              <div className="flex gap-2">
                {testimonials.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => goToSlide(index)}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      index === currentIndex
                        ? "bg-brand-accent scale-110"
                        : "bg-brand-grey hover:bg-brand-accent/50"
                    }`}
                  />
                ))}
              </div>

              <Button
                variant="ghost"
                size="icon"
                onClick={nextSlide}
                className="rounded-full hover:bg-brand-accent/10"
              >
                <ChevronRight className="w-5 h-5" />
              </Button>
            </div>
          </div>
        </animated.div>
      </div>
    </section>
  );
};

export default Testimonials;
