# The Brown Patience Company - Frontend

## Project Overview

This is the frontend application for The Brown Patience Company website, built with React, TypeScript, and Vite. The site showcases <PERSON>'s work as an author and writing coach, featuring her books, services, and blog content.

## Development Setup

**Local Development**

To work locally using your preferred IDE, clone this repo and follow these steps:

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

This project is designed to be deployed to WhoGhost cPanel hosting. Build the project using `npm run build` and upload the `dist` folder contents to your hosting provider.

## Custom Domain

The site is configured for the domain `thebrownpatiencecompany.com.ng`. Update the domain references in the code if using a different domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/features/custom-domain#custom-domain)
