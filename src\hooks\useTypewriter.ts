import { useState, useEffect, useRef } from "react";

interface TypewriterOptions {
  speed?: number;
  delay?: number;
  infinite?: boolean;
}

export const useTypewriter = (
  text: string,
  options: TypewriterOptions = {}
) => {
  const { speed = 100, delay = 1000, infinite = false } = options;

  const [displayText, setDisplayText] = useState("");
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const type = () => {
      if (isDeleting) {
        // Deleting text
        if (currentIndex > 0) {
          setDisplayText(text.substring(0, currentIndex - 1));
          setCurrentIndex((prev) => prev - 1);
        } else {
          // Finished deleting, pause then start typing again if infinite
          setIsDeleting(false);
          if (infinite) {
            timeoutRef.current = setTimeout(type, delay);
          }
        }
      } else {
        // Typing text
        if (currentIndex < text.length) {
          setDisplayText(text.substring(0, currentIndex + 1));
          setCurrentIndex((prev) => prev + 1);
        } else {
          // Finished typing, pause then start deleting
          if (infinite) {
            timeoutRef.current = setTimeout(() => {
              setIsDeleting(true);
              type();
            }, delay);
          }
        }
      }
    };

    timeoutRef.current = setTimeout(type, isDeleting ? speed / 2 : speed);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [text, currentIndex, isDeleting, speed, delay, infinite]);

  // Reset when text changes
  useEffect(() => {
    setDisplayText("");
    setCurrentIndex(0);
    setIsDeleting(false);
  }, [text]);

  return displayText;
};
