import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, Award } from "lucide-react";
import b10 from "@/assets/brown/b10.webp";
import { Link } from "react-router-dom";

const AboutSnapshot = () => {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const contentSpring = useSpring({
    opacity: inView ? 1 : 0,
    transform: inView ? "translateY(0px)" : "translateY(30px)",
  });

  const imageSpring = useSpring({
    opacity: inView ? 1 : 0,
    transform: inView
      ? "translateY(0px) rotate(0deg)"
      : "translateY(30px) rotate(-2deg)",
    delay: 200,
  });

  return (
    <section ref={ref} className="py-20 bg-hero-accent-gradient">
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <animated.div style={contentSpring}>
            <div className="space-y-6">
              <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium">
                <Award className="w-4 h-4" />
                My Story
              </div>

              <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary">
                Meet <span className="text-brand-accent">Brown Patience</span>
              </h2>

              <div className="space-y-4 text-brand-secondary/80 leading-relaxed">
                <p className="text-lg">
                  As a teen, I wrote for fun. I wrote to relieve boredom. I
                  could be sitting in a place and mentally remove myself from
                  there by scribbling a letter to an imaginary cousin in Vienna.
                  Nevermind that I had no idea if Vienna was actually a place.
                  Writing was how I went to places I wished I could be.
                </p>

                <p>
                  As soon as the English Language teacher said, "Write a letter
                  to your uncle in London," it was my lucky day! All these
                  should have told me writing was the path for me, but I
                  couldn't have known. I said I'd be a banker when I grew up; I
                  set my mind on pursuing accounting in college. And that's
                  precisely what I did. Yet purpose can and will find you.
                </p>
              </div>

              <div className="flex flex-wrap gap-6 text-brand-secondary/70 text-sm">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-brand-accent rounded-full"></div>
                  <span>5+ Years Writing Experience</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-brand-accent rounded-full"></div>
                  <span>50+ Writers Helped</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-brand-accent rounded-full"></div>
                  <span>7 Published Books</span>
                </div>
              </div>
              <Link to="/about">
                <Button variant="outline" size="lg" className="group mt-6">
                  Read My Full Story
                  <ArrowRight className="w-5 h-5 ml-1 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>
            </div>
          </animated.div>

          {/* Author Photo */}
          <animated.div style={imageSpring} className="lg:pl-8">
            <div className="relative">
              <div className="aspect-square max-w-md mx-auto rounded-2xl overflow-hidden shadow-elegant">
                <img
                  src={b10}
                  alt="Brown Patience- Author and Writing Coach"
                  className="w-full h-full object-cover object-top hover:scale-110 transition-transform duration-500"
                  loading="lazy"
                />
              </div>

              {/* Decorative elements */}
              <div className="absolute -top-4 -left-4 w-24 h-24 bg-brand-accent/10 rounded-full blur-xl"></div>
              <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-brand-accent/5 rounded-full blur-2xl"></div>

              {/* Quote bubble */}
              <div className="absolute -right-4 top-1/2 bg-brand-accent shadow-elegant rounded-2xl p-4 max-w-xs border border-brand-grey/20">
                <p className="text-sm text-brand-primary italic">
                  "Every writer has a story worth telling. My job is to help you
                  tell it beautifully."
                </p>
              </div>
            </div>
          </animated.div>
        </div>
      </div>
    </section>
  );
};

export default AboutSnapshot;
