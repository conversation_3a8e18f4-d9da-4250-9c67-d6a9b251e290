import { useState, useEffect } from "react";
import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  ExternalLink,
  BookOpen,
  ArrowRight,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import soHeTaughtMeNo from "@/assets/cover/soHeTaughtMeNo.webp";
import chimamanda from "@/assets/cover/chimamanda.webp";
import fantasy from "@/assets/cover/fantasy.webp";

interface Book {
  id: string;
  title: string;
  cover: string;
  description: string;
  buyUrl: string;
}

const books: Book[] = [
  {
    id: "1",
    title: "So He Taught Me 'No'",
    cover: soHeTaughtMeNo,
    description:
      "This is a book about porn addiction. About the darkness that persists even after you've stopped viewing it. All based on true experience. It's a book about mind renewal. A book about how the Holy Spirit teaches you to say 'No' to ungodliness — no matter how strong the mental stronghold.",
    buyUrl: "#",
  },
  {
    id: "2",
    title: "<PERSON><PERSON><PERSON><PERSON>",
    cover: chimamanda,
    description:
      "Born to a man without warmth, <PERSON>mamanda is married off early to a man she dreads just as much as her father. But her walls are in place; she is safe — as long as she stays strong. She would have been content living as she had always lived. But God has His way of squeezing out what life you thought you had so He can give you true life.",
    buyUrl: "#",
  },
  {
    id: "3",
    title: "Fantasy",
    cover: fantasy,
    description:
      "Fantasy is about the sexual mental struggles we face. Those steamy thoughts that feel like you're engaging in a porn video production. Fantasy is for the young woman who'd like her thoughts to honor God, who'd like to bring her thoughts under the Holy Spirit's control.",
    buyUrl: "#",
  },
];

const FeaturedBooks = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const sectionSpring = useSpring({
    opacity: inView ? 1 : 0,
    transform: inView ? "translateY(0px)" : "translateY(50px)",
  });

  // Auto-advance carousel
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % books.length);
    }, 10000); // Change every 10 seconds

    return () => clearInterval(timer);
  }, []);

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % books.length);
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + books.length) % books.length);
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  const trackBookClick = (bookId: string, title: string) => {
    // Analytics tracking would go here
    console.log("Book click tracked:", { bookId, title });
  };

  return (
    <section id="books" ref={ref} className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <animated.div style={sectionSpring}>
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium mb-4">
              <BookOpen className="w-4 h-4" />
              Featured Publications
            </div>
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
              My Book Section
            </h2>
            <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
              Some of these books have been described as brutally honest—with
              regard to the I'll-tell-it-as-it-is description of addiction.
              Reviews have been utterly intimate, held safe in my heart.
            </p>
          </div>

          {/* Books Carousel */}
          <div className="max-w-6xl mx-auto">
            <div className="relative overflow-hidden rounded-2xl min-h-[800px] sm:min-h-[900px] md:min-h-[800px] lg:min-h-[700px]">
              {books.map((book, index) => {
                const isEven = index % 2 === 0;
                const isActive = index === currentIndex;

                return (
                  <animated.div
                    key={book.id}
                    style={{
                      opacity: isActive ? 1 : 0,
                      transform: isActive ? "scale(1)" : "scale(0.95)",
                      position: "absolute",
                      inset: 0,
                      width: "100%",
                      transition: "all 0.8s ease-in-out",
                      zIndex: isActive ? 1 : 0,
                    }}
                  >
                    <Card className="group hover:shadow-elegant transition-all duration-300 interactive-lift overflow-hidden h-full">
                      <CardContent className="p-0 h-full">
                        <div
                          className={`flex flex-col lg:flex-row ${
                            !isEven ? "lg:flex-row-reverse" : ""
                          } h-full`}
                        >
                          {/* Book Cover */}
                          <div className="w-full lg:w-1/2 aspect-[4/5] md:aspect-[3/4] lg:aspect-[2/3] min-h-[300px] md:min-h-[400px] lg:min-h-[500px]">
                            <img
                              src={book.cover}
                              alt={`Cover of ${book.title}`}
                              className="w-full h-full object-cover object-center group-hover:scale-105 transition-transform duration-500"
                              loading="lazy"
                            />
                          </div>

                          {/* Book Content */}
                          <div className="w-full lg:w-1/2 h-auto p-6 md:p-8 lg:p-12 flex flex-col justify-center">
                            <div className="space-y-4 md:space-y-6">
                              <div>
                                <h3 className="text-xl md:text-2xl lg:text-3xl font-serif font-bold text-brand-secondary mb-3 md:mb-4">
                                  {book.title}
                                </h3>
                                <p className="text-brand-secondary/80 leading-relaxed text-base md:text-lg">
                                  {book.description}
                                </p>
                              </div>

                              {/* Action Button */}
                              <div className="pt-2 md:pt-4">
                                <Button
                                  variant="hero"
                                  size="lg"
                                  onClick={() => {
                                    trackBookClick(book.id, book.title);
                                    window.open(
                                      book.buyUrl,
                                      "_blank",
                                      "noopener,noreferrer"
                                    );
                                  }}
                                  className="group/btn"
                                >
                                  <ExternalLink className="w-5 h-5 mr-2" />
                                  Get Your Copy
                                  <ArrowRight className="w-5 h-5 ml-2 group-hover/btn:translate-x-1 transition-transform" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </animated.div>
                );
              })}
            </div>

            {/* Navigation Controls */}
            <div className="flex items-center justify-center gap-4 mt-8">
              <Button
                variant="ghost"
                size="icon"
                onClick={prevSlide}
                className="rounded-full hover:bg-brand-accent/10 backdrop-blur-sm"
              >
                <ChevronLeft className="w-5 h-5" />
              </Button>

              {/* Dots Indicator */}
              <div className="flex gap-2">
                {books.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => goToSlide(index)}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      index === currentIndex
                        ? "bg-brand-accent scale-110"
                        : "bg-brand-grey hover:bg-brand-accent/50"
                    }`}
                  />
                ))}
              </div>

              <Button
                variant="ghost"
                size="icon"
                onClick={nextSlide}
                className="rounded-full hover:bg-brand-accent/10 backdrop-blur-sm"
              >
                <ChevronRight className="w-5 h-5" />
              </Button>
            </div>

            {/* Progress Indicator */}
            <div className="flex items-center justify-center mt-4">
              <div className="text-sm text-brand-secondary/60">
                {currentIndex + 1} of {books.length}
              </div>
            </div>
          </div>

          {/* View All Books Button */}
          <div className="text-center mt-12">
            <Button
              variant="outline"
              size="lg"
              onClick={() => (window.location.href = "/books")}
              className="group"
            >
              View All Books
              <ArrowRight className="w-5 h-5 ml-1 group-hover:translate-x-1 transition-transform" />
            </Button>
          </div>
        </animated.div>
      </div>
    </section>
  );
};

export default FeaturedBooks;
