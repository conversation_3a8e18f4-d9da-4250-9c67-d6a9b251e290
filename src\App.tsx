import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { HelmetProvider } from "react-helmet-async";
import { Suspense, lazy, useEffect } from "react";
import { Toaster } from "@/components/ui/toaster";
import Header from "./layout/Header";
import Footer from "./layout/Footer";

// Lazy load all page components
const Home = lazy(() => import("./pages/Home"));
const About = lazy(() => import("./pages/About"));
const Books = lazy(() => import("./pages/Books"));
const BlogList = lazy(() => import("./pages/Blog/BlogList"));
const Community = lazy(() => import("./pages/Community"));
const ContactPage = lazy(() => import("./pages/Contact"));
const GuidanceForSolopreneurs = lazy(
  () => import("./pages/Subscriptions/GuidanceForSolopreneurs")
);
const CoachingForAuthors = lazy(
  () => import("./pages/Subscriptions/CoachingForAuthors")
);
const BookWritingEditing = lazy(
  () => import("./pages/Services/BookWritingEditing")
);
const CoachingForWriters = lazy(
  () => import("./pages/Services/CoachingForWriters")
);
const ContentWriting = lazy(() => import("./pages/Services/ContentWriting"));

// Loading component with better UX
const PageLoader = () => (
  <div className="flex flex-col items-center justify-center min-h-[50vh] space-y-4">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-accent"></div>
    <p className="text-brand-secondary/70 text-sm animate-pulse">
      Loading page...
    </p>
  </div>
);

function App() {
  // Preload critical routes after initial load
  useEffect(() => {
    const preloadRoutes = () => {
      // Preload About and Services pages as they're commonly visited
      import("./pages/About");
      import("./pages/Services/BookWritingEditing");
    };

    // Preload after a short delay to not interfere with initial load
    const timer = setTimeout(preloadRoutes, 2000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <HelmetProvider>
      <Router>
        <div className="min-h-screen bg-cream-50">
          <Header />
          <main className="pt-24">
            <Suspense fallback={<PageLoader />}>
              <Routes>
                <Route path="/" element={<Home />} />
                <Route path="/about" element={<About />} />
                <Route path="/books" element={<Books />} />
                <Route path="/blog" element={<BlogList />} />

                <Route
                  path="/subscriptions/guidance-for-solopreneurs"
                  element={<GuidanceForSolopreneurs />}
                />
                <Route
                  path="/subscriptions/coaching-for-authors"
                  element={<CoachingForAuthors />}
                />
                <Route path="/community" element={<Community />} />

                {/* Service Pages */}
                <Route
                  path="/services/book-writing-editing"
                  element={<BookWritingEditing />}
                />
                <Route
                  path="/services/coaching-for-writers"
                  element={<CoachingForWriters />}
                />
                <Route
                  path="/services/content-writing"
                  element={<ContentWriting />}
                />

                <Route path="/contact" element={<ContactPage />} />
              </Routes>
            </Suspense>
          </main>
          <Footer />
          <Toaster />
        </div>
      </Router>
    </HelmetProvider>
  );
}

export default App;
