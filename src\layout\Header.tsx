import { useState, useEffect } from "react";
import { useSpring, animated } from "@react-spring/web";
import { But<PERSON> } from "@/components/ui/button";
import { Menu, X, ChevronDown } from "lucide-react";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import Logo from "@/assets/logo.webp";
import { Link } from "react-router-dom";

const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const headerSpring = useSpring({
    backgroundColor: isScrolled
      ? "rgba(255, 255, 255, 0.8)"
      : "rgba(255, 255, 255, 1)",
    backdropFilter: isScrolled ? "blur(12px)" : "blur(0px)",
    boxShadow: isScrolled
      ? "0 4px 20px rgba(0, 0, 0, 0.1)"
      : "0 0 0 rgba(0, 0, 0, 0)",
  });

  const navItems = [
    { label: "Home", href: "/" },
    { label: "About", href: "/about" },
    { label: "Books", href: "/books" },
  ];

  const serviceItems = [
    {
      label: "Book Writing and Editing",
      href: "/services/book-writing-editing",
    },
    { label: "Coaching for Writers", href: "/services/coaching-for-writers" },
    { label: "Content Writing", href: "/services/content-writing" },
  ];

  const subscriptionItems = [
    {
      label: "Guidance for Solopreneurs",
      href: "/subscriptions/guidance-for-solopreneurs",
    },
    {
      label: "Coaching for Authors",
      href: "/subscriptions/coaching-for-authors",
    },
  ];

  const scrollToNewsletter = () => {
    document
      .getElementById("newsletter")
      ?.scrollIntoView({ behavior: "smooth" });
    const input = document.getElementById("newsletter-email");
    setTimeout(() => input?.focus(), 500);
  };

  return (
    <animated.header
      style={headerSpring}
      className="fixed top-0 left-0 right-0 z-50 border-b border-border/20"
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center gap-2">
            {/* Desktop/Tablet Logo - visible from sm (640px) and up */}
            <Link to="/">
              <img src={Logo} alt="Author Logo" className=" h-10 w-auto" />
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center gap-8">
            <NavigationMenu>
              <NavigationMenuList>
                {navItems.slice(0, 3).map((item) => (
                  <NavigationMenuItem key={item.label}>
                    <NavigationMenuLink
                      href={item.href}
                      className="text-brand-secondary hover:text-brand-accent transition-colors duration-300 font-medium px-3 py-2"
                    >
                      {item.label}
                    </NavigationMenuLink>
                  </NavigationMenuItem>
                ))}

                {/* Services Dropdown */}
                <NavigationMenuItem>
                  <NavigationMenuTrigger className="text-brand-secondary text-base hover:text-brand-accent transition-colors duration-300 font-medium bg-transparent hover:bg-transparent">
                    Services
                  </NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <div className="grid w-[400px] gap-3 p-4">
                      {serviceItems.map((service) => (
                        <NavigationMenuLink
                          key={service.label}
                          href={service.href}
                          className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                        >
                          <div className="text-sm font-medium leading-none">
                            {service.label}
                          </div>
                        </NavigationMenuLink>
                      ))}
                    </div>
                  </NavigationMenuContent>
                </NavigationMenuItem>

                {/* Subscriptions Dropdown */}
                <NavigationMenuItem>
                  <NavigationMenuTrigger className="text-brand-secondary text-base hover:text-brand-accent transition-colors duration-300 font-medium bg-transparent hover:bg-transparent">
                    Subscriptions
                  </NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <div className="grid w-[400px] gap-3 p-4">
                      {subscriptionItems.map((subscription) => (
                        <NavigationMenuLink
                          key={subscription.label}
                          href={subscription.href}
                          className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                        >
                          <div className="text-sm font-medium leading-none">
                            {subscription.label}
                          </div>
                        </NavigationMenuLink>
                      ))}
                    </div>
                  </NavigationMenuContent>
                </NavigationMenuItem>

                {/* Community Link */}
                <NavigationMenuItem>
                  <NavigationMenuLink
                    href="/community"
                    className="text-brand-secondary hover:text-brand-accent transition-colors duration-300 font-medium px-3 py-2"
                  >
                    Community
                  </NavigationMenuLink>
                </NavigationMenuItem>

                {navItems.slice(3).map((item) => (
                  <NavigationMenuItem key={item.label}>
                    <NavigationMenuLink
                      href={item.href}
                      className="text-brand-secondary hover:text-brand-accent transition-colors duration-300 font-medium px-3 py-2"
                    >
                      {item.label}
                    </NavigationMenuLink>
                  </NavigationMenuItem>
                ))}
              </NavigationMenuList>
            </NavigationMenu>
          </nav>

          {/* Mobile Menu */}
          <div className="lg:hidden">
            <Dialog open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
              <DialogTrigger asChild>
                <Button variant="ghost" size="icon">
                  <Menu className="w-5 h-5" />
                </Button>
              </DialogTrigger>
              <DialogContent className="w-full max-w-sm">
                <div className="flex flex-col gap-4 p-6">
                  {navItems.map((item) => (
                    <a
                      key={item.label}
                      href={item.href}
                      onClick={() => setIsMobileMenuOpen(false)}
                      className="text-brand-secondary hover:text-brand-accent transition-colors duration-300 font-medium py-2"
                    >
                      {item.label}
                    </a>
                  ))}

                  {/* Services Section */}
                  <div className="border-t border-brand-grey/30 pt-4 mt-4">
                    <span className="text-sm font-semibold text-brand-secondary/60 mb-2 block">
                      Services
                    </span>
                    {serviceItems.map((service) => (
                      <a
                        key={service.label}
                        href={service.href}
                        onClick={() => setIsMobileMenuOpen(false)}
                        className="text-brand-secondary hover:text-brand-accent transition-colors duration-300 font-medium py-2 pl-4 block"
                      >
                        {service.label}
                      </a>
                    ))}
                  </div>

                  {/* Subscriptions Section */}
                  <div className="border-t border-brand-grey/30 pt-4 mt-4">
                    <span className="text-sm font-semibold text-brand-secondary/60 mb-2 block">
                      Subscriptions
                    </span>
                    {subscriptionItems.map((subscription) => (
                      <a
                        key={subscription.label}
                        href={subscription.href}
                        onClick={() => setIsMobileMenuOpen(false)}
                        className="text-brand-secondary hover:text-brand-accent transition-colors duration-300 font-medium py-2 pl-4 block"
                      >
                        {subscription.label}
                      </a>
                    ))}
                  </div>

                  {/* Community Link */}
                  <a
                    href="/community"
                    onClick={() => setIsMobileMenuOpen(false)}
                    className="text-brand-secondary hover:text-brand-accent transition-colors duration-300 font-medium py-2"
                  >
                    Community
                  </a>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>
    </animated.header>
  );
};

export default Header;
