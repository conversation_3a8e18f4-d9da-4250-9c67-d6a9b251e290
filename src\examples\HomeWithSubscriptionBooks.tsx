import { useEffect } from "react";
import "animate.css";

import MetaTags from "@/components/MetaTags";
import Hero from "@/components/Hero";
import FeaturedBooks from "@/components/FeaturedBooks";
import AboutSnapshot from "@/components/AboutSnapshot";
import Services from "@/components/Services";
import MyWorks from "@/components/MyWorks";
import Subscription from "@/components/Subscription";
import SubscriptionBooks from "@/components/Subscription"; // New component
import BlogPosts from "@/components/BlogPosts";
import Testimonials from "@/components/Testimonials";
import Contact from "@/components/Contact";

const HomeWithSubscriptionBooks = () => {
  useEffect(() => {
    // Smooth scrolling for anchor links
    const handleSmoothScroll = (e: Event) => {
      const target = e.target as HTMLAnchorElement;
      if (target.hash) {
        e.preventDefault();
        const element = document.querySelector(target.hash);
        if (element) {
          element.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }
      }
    };

    const links = document.querySelectorAll('a[href^="#"]');
    links.forEach((link) => {
      link.addEventListener("click", handleSmoothScroll);
    });

    return () => {
      links.forEach((link) => {
        link.removeEventListener("click", handleSmoothScroll);
      });
    };
  }, []);

  return (
    <>
      <MetaTags
        title="Brown Patience - Author & Writing Coach"
        description="Get the help you need to write your book, to share your message — clearly, compellingly. Professional writing and editing services."
        image="/og-images/home.webp"
        url="https://thebrownpatiencecompany.com.ng"
        keywords="writing coach, author, publishing, book writing, editorial services, creative writing, Brown Patience"
      />
      <div className="min-h-screen bg-background text-foreground">
        {/* Main Content */}
        <main id="main-content">
          {/* Hero Section */}
          <Hero />

          {/* Services */}
          <Services />

          {/* About Snapshot */}
          <AboutSnapshot />

          {/* Featured Books */}
          <FeaturedBooks />

          {/* NEW: Subscription Books Section - Horizontal cards with carousel */}
          <SubscriptionBooks />

          {/* Original Subscription Plans - Vertical cards */}
          <Subscription />

          {/* My Works Portfolio */}
          <MyWorks />

          {/* Testimonials */}
          <Testimonials />

          {/* Latest Blog Posts */}
          <BlogPosts />

          {/* Contact Section */}
          <Contact />
        </main>
      </div>
    </>
  );
};

export default HomeWithSubscriptionBooks;

/**
 * INTEGRATION NOTES:
 *
 * 1. The new SubscriptionBooks component is placed between FeaturedBooks and the original Subscription component
 * 2. This creates a logical flow: Books → Writing Resources → Subscription Plans
 * 3. The component follows the same design patterns as existing components
 * 4. It uses the same responsive breakpoints (lg: 1024px)
 * 5. All styling is consistent with the existing design system
 *
 * ALTERNATIVE PLACEMENT OPTIONS:
 *
 * Option A: Replace the original Subscription component entirely
 * - Simply replace <Subscription /> with <SubscriptionBooks />
 *
 * Option B: Place after Services section
 * - Move <SubscriptionBooks /> right after <Services />
 * - This would highlight writing resources earlier in the page flow
 *
 * Option C: Create a dedicated Resources page
 * - Use SubscriptionBooks as the main component on a new /resources route
 * - Add navigation link in Header component
 *
 * RESPONSIVE BEHAVIOR:
 *
 * Desktop (lg and above):
 * - Shows 2x2 grid of horizontal cards
 * - Image on left, content on right
 * - Hover effects and animations
 *
 * Mobile (below lg):
 * - Carousel with single card view
 * - Vertical layout with image on top
 * - Auto-advance every 8 seconds
 * - Navigation arrows and dots
 * - Progress indicator
 *
 * ACCESSIBILITY FEATURES:
 *
 * - Proper alt text for all images
 * - Keyboard navigation support
 * - Focus management for carousel controls
 * - Screen reader friendly structure
 * - Reduced motion support
 */
