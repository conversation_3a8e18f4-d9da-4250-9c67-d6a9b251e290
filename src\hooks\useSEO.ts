import { useEffect } from "react";
import { updateMetaTags, SEOConfig } from "@/lib/seo";

/**
 * Custom hook for managing SEO and Open Graph meta tags
 * @param config SEO configuration object
 */
export function useSEO(config: SEOConfig) {
  useEffect(() => {
    // Update meta tags when component mounts or config changes
    updateMetaTags(config);

    // Optionally update the document title
    if (config.title) {
      document.title = config.title;
    }

    // Cleanup function (optional)
    return () => {
      // Reset to default SEO config if needed
      // updateMetaTags(defaultSEOConfig);
    };
  }, [config]);
}
