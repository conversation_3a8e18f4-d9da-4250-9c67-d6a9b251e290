import { Helmet } from "react-helmet-async";

interface MetaTagsProps {
  title?: string;
  description?: string;
  image?: string;
  url?: string;
  type?: string;
  keywords?: string;
}

const MetaTags: React.FC<MetaTagsProps> = ({
  title = "Brown Patience - Author & Writing Coach",
  description = "Get the help you need to write your book, to share your message — clearly, compellingly. Professional writing and editing services.",
  image = "/og-images/home.webp",
  url = "https://thebrownpatiencecompany.com.ng",
  type = "website",
  keywords = "writing coach, author, publishing, book writing, editorial services, creative writing, Brown Patience",
}) => {
  // Ensure image URL is absolute
  const fullImageUrl = image.startsWith("http")
    ? image
    : `https://thebrownpatiencecompany.com.ng${image}`;

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="author" content="Brown Patience" />

      {/* Open Graph / Facebook */}
      <meta property="og:type" content={type} />
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={fullImageUrl} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:image:alt" content={title} />
      <meta property="og:url" content={url} />
      <meta property="og:site_name" content="The Brown Patience Company" />

      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={fullImageUrl} />
      <meta name="twitter:image:alt" content={title} />

      {/* LinkedIn */}
      <meta property="og:image:type" content="image/jpeg" />

      {/* Canonical URL */}
      <link rel="canonical" href={url} />

      {/* Additional SEO Meta Tags */}
      <meta name="robots" content="index, follow" />
      <meta name="language" content="English" />
      <meta name="revisit-after" content="7 days" />
    </Helmet>
  );
};

export default MetaTags;
