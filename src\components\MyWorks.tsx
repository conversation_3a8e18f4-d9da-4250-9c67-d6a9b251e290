import { useState, useEffect } from "react";
import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ExternalLink,
  BookOpen,
  Users,
  Award,
  Calendar,
  Star,
  Quote,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import booksCollection from "@/assets/books-collection.jpg";

// Portfolio images
import educellImg from "@/assets/cover/edusell.webp";
import deborahImg from "@/assets/cover/deborah.webp";
import envisionImg from "@/assets/cover/envision.webp";
import onpointImg from "@/assets/cover/onPoint.webp";
import billionaireImg from "@/assets/cover/billionaire.webp";
import battlesImg from "@/assets/cover/battles.webp";

interface Work {
  id: string;
  title: string;
  category: "book" | "coaching" | "workshop" | "article";
  description: string;
  image: string;
  date: string;
  tags: string[];
  link?: string;
  stats?: {
    label: string;
    value: string;
  }[];
  testimonial?: {
    text: string;
    author: string;
    role: string;
  };
}

const works: Work[] = [
  {
    id: "1",
    title: "Edusell",
    category: "book",
    description:
      "When this manuscript got to me, I was told it had been edited already. I was glad it hadn't been published in that state. Cause it 'needed' work.",
    image: educellImg,
    date: "2023",
    tags: ["Editing", "Restructuring"],
    stats: [
      { label: "Chapters", value: "Reorganized" },
      { label: "Sections", value: "Grouped" },
      { label: "Flow", value: "Enhanced" },
    ],
  },
  {
    id: "2",
    title: "A Gift To Deborah",
    category: "book",
    description:
      "Precious Ayomikun carries the girl child in her thoughts. When she decided to write a book for young women, detailing her own stroll through the corridors of depression and inadequacy, I was glad to collaborate with her.",
    image: deborahImg,
    date: "2023",
    tags: ["Collaboration", "Young Women"],
    testimonial: {
      text: "Wherever a young woman finds this book, it'll be a gift indeed.",
      author: "Precious Ayomikun",
      role: "Author",
    },
  },
  {
    id: "3",
    title: "Envision",
    category: "book",
    description:
      "This Nigerian-born medical doctor could have been prescribing medication only for the body, but she decided to craft medicine for the mind as well. Doctor Oyindamola wrote a short, precise, and sharp book targeted at the ones who want to live purposefully.",
    image: envisionImg,
    date: "2022",
    tags: ["Medical Professional", "Purpose"],
    stats: [
      { label: "Focus", value: "Purposeful Living" },
      { label: "Style", value: "Precise & Sharp" },
      { label: "Impact", value: "Mind Medicine" },
    ],
  },
  {
    id: "4",
    title: "On Point",
    category: "book",
    description:
      "Days after its author released this pointed missive into the world, the number of downloads and reviews assured me afresh of what I'd known since I saw the manuscript: Olamide wrote a necessary book.",
    image: onpointImg,
    date: "2022",
    tags: ["Necessary Message", "High Impact"],
    stats: [
      { label: "Downloads", value: "High Volume" },
      { label: "Reviews", value: "Excellent" },
      { label: "Impact", value: "Necessary" },
    ],
  },
  {
    id: "5",
    title: "Billionaire Codes",
    category: "book",
    description:
      "The personnel in charge of getting Dr. Stephen Akintayo's Billionaire Codes edited sought an editor who could cut out repetitions, tighten every chapter, and sharpen the message. It was my honor to serve as the editor they needed.",
    image: billionaireImg,
    date: "2021",
    tags: ["Business", "Editing Excellence"],
    stats: [
      { label: "Repetitions", value: "Eliminated" },
      { label: "Chapters", value: "Tightened" },
      { label: "Message", value: "Sharpened" },
    ],
  },
  {
    id: "6",
    title: "7 Battles Every Trader Must Fight to Win",
    category: "book",
    description:
      "A cryptocurrency expert, a coach to many, Mayowa Owolabi certainly knows the struggles of traders. When he needed the videos from his coaching sessions transcribed into this book, I was glad to make the dream a hardcover reality.",
    image: battlesImg,
    date: "2021",
    tags: ["Cryptocurrency", "Transcription"],
    stats: [
      { label: "Source", value: "Video Sessions" },
      { label: "Format", value: "Hardcover" },
      { label: "Expertise", value: "Trading" },
    ],
  },
];

const categoryIcons = {
  book: BookOpen,
  coaching: Users,
  workshop: Award,
  article: Quote,
};

const categoryColors = {
  book: "bg-brand-accent/10 text-brand-accent",
  coaching: "bg-blue-100 text-blue-600",
  workshop: "bg-green-100 text-green-600",
  article: "bg-purple-100 text-purple-600",
};

const MyWorks = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const sectionSpring = useSpring({
    opacity: inView ? 1 : 0,
    transform: inView ? "translateY(0px)" : "translateY(50px)",
  });

  // Check if mobile view
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024); // lg breakpoint
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  // Auto-advance carousel on mobile
  useEffect(() => {
    if (!isMobile) return;

    const timer = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % works.length);
    }, 4000); // Change every 4 seconds

    return () => clearInterval(timer);
  }, [isMobile]);

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % works.length);
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + works.length) % works.length);
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  return (
    <section id="myworks" ref={ref} className="py-20 bg-brand-neutral/30">
      <div className="container mx-auto px-4">
        <animated.div style={sectionSpring}>
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium mb-4">
              <Award className="w-4 h-4" />
              Portfolio & Impact
            </div>
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
              Books I've Worked On
            </h2>
            <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
              A showcase of the books and projects I've helped bring to life
              through professional editing, writing, and coaching services.
            </p>
          </div>

          {/* Works Grid - Desktop */}
          <div className="hidden lg:grid lg:grid-cols-3 gap-8">
            {works.map((work, index) => {
              const CategoryIcon = categoryIcons[work.category];

              return (
                <animated.div
                  key={work.id}
                  style={{
                    opacity: inView ? 1 : 0,
                    transform: inView ? "translateY(0px)" : "translateY(50px)",
                    transitionDelay: inView ? `${200 + index * 100}ms` : "0ms",
                    transition: "all 0.6s ease-out",
                  }}
                >
                  <Card className="group h-full hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1 interactive-lift">
                    <CardHeader className="p-0">
                      <div className="aspect-[4/3] rounded-t-lg overflow-hidden relative">
                        <img
                          src={work.image}
                          alt={work.title}
                          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                          loading="lazy"
                        />
                        <div className="absolute top-4 left-4">
                          <Badge className={categoryColors[work.category]}>
                            <CategoryIcon className="w-3 h-3 mr-1" />
                            {work.category.charAt(0).toUpperCase() +
                              work.category.slice(1)}
                          </Badge>
                        </div>
                        <div className="absolute top-4 right-4">
                          <div className="flex items-center gap-1 text-sm text-brand-secondary/60 bg-white/90 px-2 py-1 rounded">
                            <Calendar className="w-3 h-3" />
                            {work.date}
                          </div>
                        </div>
                      </div>
                    </CardHeader>

                    <CardContent className="p-6">
                      <div className="space-y-4">
                        {/* Title & Description */}
                        <div>
                          <h3 className="text-xl font-serif font-bold text-brand-secondary mb-2">
                            {work.title}
                          </h3>
                          <p className="text-brand-secondary/80 leading-relaxed text-sm">
                            {work.description}
                          </p>
                        </div>

                        {/* Tags */}
                        <div className="flex flex-wrap gap-2">
                          {work.tags.map((tag) => (
                            <span
                              key={tag}
                              className="px-2 py-1 bg-brand-accent/10 text-brand-accent text-xs font-medium rounded-full"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>

                        {/* Stats */}
                        {work.stats && (
                          <div className="grid grid-cols-3 gap-2 text-center">
                            {work.stats.map((stat, idx) => (
                              <div
                                key={idx}
                                className="bg-brand-neutral/50 rounded-lg p-2"
                              >
                                <div className="text-lg font-bold text-brand-accent">
                                  {stat.value}
                                </div>
                                <div className="text-xs text-brand-secondary/60">
                                  {stat.label}
                                </div>
                              </div>
                            ))}
                          </div>
                        )}

                        {/* Testimonial */}
                        {work.testimonial && (
                          <div className="bg-brand-primary border-l-4 border-brand-accent p-3 rounded">
                            <p className="text-sm text-brand-secondary/80 italic mb-2">
                              "{work.testimonial.text}"
                            </p>
                            <div className="text-xs text-brand-secondary/60">
                              - {work.testimonial.author},{" "}
                              {work.testimonial.role}
                            </div>
                          </div>
                        )}

                        {/* Action */}
                        <div className="pt-2">
                          {work.link ? (
                            <Button
                              variant="outline"
                              size="sm"
                              className="w-full group"
                              onClick={() =>
                                window.open(
                                  work.link,
                                  "_blank",
                                  "noopener,noreferrer"
                                )
                              }
                            >
                              <ExternalLink className="w-4 h-4 mr-1" />
                              View Work
                            </Button>
                          ) : (
                            <Button
                              variant="outline"
                              size="sm"
                              className="w-full"
                              disabled
                            >
                              Private Work
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </animated.div>
              );
            })}
          </div>

          {/* Works Carousel - Mobile */}
          <div className="lg:hidden">
            <div className="relative overflow-hidden rounded-2xl">
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentIndex * 100}%)` }}
              >
                {works.map((work) => {
                  const CategoryIcon = categoryIcons[work.category];

                  return (
                    <div key={work.id} className="w-full flex-shrink-0 px-2">
                      <Card className="group h-full hover:shadow-elegant transition-all duration-300 interactive-lift mx-auto max-w-sm">
                        <CardHeader className="p-0">
                          <div className="aspect-[4/3] rounded-t-lg overflow-hidden relative">
                            <img
                              src={work.image}
                              alt={work.title}
                              className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                              loading="lazy"
                            />
                            <div className="absolute top-3 left-3">
                              <Badge className={categoryColors[work.category]}>
                                <CategoryIcon className="w-3 h-3 mr-1" />
                                {work.category.charAt(0).toUpperCase() +
                                  work.category.slice(1)}
                              </Badge>
                            </div>
                            <div className="absolute top-3 right-3">
                              <div className="flex items-center gap-1 text-xs text-brand-secondary/60 bg-white/90 px-2 py-1 rounded">
                                <Calendar className="w-3 h-3" />
                                {work.date}
                              </div>
                            </div>
                          </div>
                        </CardHeader>

                        <CardContent className="p-4">
                          <div className="space-y-3">
                            {/* Title & Description */}
                            <div>
                              <h3 className="text-lg font-serif font-bold text-brand-secondary mb-2">
                                {work.title}
                              </h3>
                              <p className="text-brand-secondary/80 leading-relaxed text-sm line-clamp-3">
                                {work.description}
                              </p>
                            </div>

                            {/* Tags */}
                            <div className="flex flex-wrap gap-1">
                              {work.tags.slice(0, 2).map((tag) => (
                                <span
                                  key={tag}
                                  className="px-2 py-1 bg-brand-accent/10 text-brand-accent text-xs font-medium rounded-full"
                                >
                                  {tag}
                                </span>
                              ))}
                            </div>

                            {/* Stats - Compact */}
                            {work.stats && (
                              <div className="grid grid-cols-3 gap-1 text-center">
                                {work.stats.slice(0, 3).map((stat, idx) => (
                                  <div
                                    key={idx}
                                    className="bg-brand-neutral/50 rounded p-1"
                                  >
                                    <div className="text-sm font-bold text-brand-accent">
                                      {stat.value}
                                    </div>
                                    <div className="text-xs text-brand-secondary/60">
                                      {stat.label}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            )}

                            {/* Action */}
                            <div className="pt-2">
                              {work.link ? (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="w-full group text-sm"
                                  onClick={() =>
                                    window.open(
                                      work.link,
                                      "_blank",
                                      "noopener,noreferrer"
                                    )
                                  }
                                >
                                  <ExternalLink className="w-3 h-3 mr-1" />
                                  View Work
                                </Button>
                              ) : (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="w-full text-sm"
                                  disabled
                                >
                                  Private Work
                                </Button>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Mobile Carousel Navigation */}
            <div className="flex items-center justify-center gap-4 mt-6">
              <Button
                variant="ghost"
                size="icon"
                onClick={prevSlide}
                className="rounded-full hover:bg-brand-accent/10 h-8 w-8"
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>

              {/* Dots */}
              <div className="flex gap-1">
                {works.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => goToSlide(index)}
                    className={`w-2 h-2 rounded-full transition-all duration-300 ${
                      index === currentIndex
                        ? "bg-brand-accent scale-110"
                        : "bg-brand-grey hover:bg-brand-accent/50"
                    }`}
                  />
                ))}
              </div>

              <Button
                variant="ghost"
                size="icon"
                onClick={nextSlide}
                className="rounded-full hover:bg-brand-accent/10 h-8 w-8"
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>

            {/* Progress Indicator */}
            <div className="text-center mt-3">
              <div className="text-sm text-brand-secondary/60">
                {currentIndex + 1} of {works.length}
              </div>
            </div>
          </div>

          {/* Bottom CTA */}
          <div className="text-center mt-16">
            <div className="bg-brand-primary rounded-2xl p-8 shadow-elegant max-w-2xl mx-auto">
              <div className="flex items-center justify-center gap-2 mb-4">
                <Star className="w-6 h-6 text-brand-accent" />
                <Star className="w-6 h-6 text-brand-accent" />
                <Star className="w-6 h-6 text-brand-accent" />
                <Star className="w-6 h-6 text-brand-accent" />
                <Star className="w-6 h-6 text-brand-accent" />
              </div>
              <h3 className="text-2xl font-serif font-bold text-brand-secondary mb-4">
                Ready to Create Your Success Story?
              </h3>
              <p className="text-brand-secondary/70 mb-6">
                Join the hundreds of writers who have transformed their craft
                and achieved their publishing dreams.
              </p>
              <Button
                variant="hero"
                size="lg"
                onClick={() =>
                  document
                    .getElementById("contact")
                    ?.scrollIntoView({ behavior: "smooth" })
                }
                className="group"
              >
                Start Your Journey
                <ExternalLink className="w-5 h-5 ml-1 group-hover:translate-x-1 transition-transform" />
              </Button>
            </div>
          </div>
        </animated.div>
      </div>
    </section>
  );
};

export default MyWorks;
