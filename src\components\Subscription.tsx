import { useState, useEffect } from "react";
import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  ArrowRight,
  BookOpen,
  ChevronLeft,
  ChevronRight,
  Check,
} from "lucide-react";

// Import book cover images
import artOfWritingCover from "@/assets/cover/artOfWriting.webp";
import solopreneursCover from "@/assets/cover/solopreneurs.webp";
import coachingForAuthorsCover from "@/assets/cover/coachingForAuthors.webp";
import newcomerCover from "@/assets/cover/Newcomer.webp";

interface SubscriptionItem {
  id: string;
  title: string;
  cover: string;
  description: string;
  features: string[];
  price: string;
  billingPeriod: string;
  popular?: boolean;
}

const subscriptionItems: SubscriptionItem[] = [
  {
    id: "newcomer",
    title: "Newcomer to Book Writing",
    cover: newcomerCover,
    description:
      "A comprehensive guide to help you write your book from start to finish.",
    features: [
      "Clarify what you want to say",
      "Identify your target audience",
      "Understand why your message matters",
      "Step-by-step chapter guidance",
      "Includes practical workbook",
      "Reusable for multiple books",
    ],
    price: " 40,000",
    billingPeriod: "One Time Payment",
  },
  {
    id: "art-of-writing",
    title: "The Art of Writing Compelling Stories",
    cover: artOfWritingCover,
    description: "A 4-week course that teaches you to tell stories that move.",
    features: [
      "4-week course on writing compelling stories",
      "Learn to write like the writers you admire",
      "Master storytelling techniques",
      "Develop your unique voice",
    ],
    price: "₦25,000",
    billingPeriod: "/month",
  },
  {
    id: "solopreneurs",
    title: "Guidance for Writing Solopreneurs",
    cover: solopreneursCover,
    description:
      "Professional proofreading, editing, and necessary revisions to your Christian content.",
    features: [
      "Professional proofreading",
      "Content editing services",
      "Necessary revisions included",
      "Christian content focus",
      "Monthly consultation",
    ],
    price: "₦25,000",
    billingPeriod: "/month",
    popular: true,
  },
  {
    id: "coaching-authors",
    title: "Coaching For Authors",
    cover: coachingForAuthorsCover,
    description:
      "The coaching package where I guide you as you author your book, chapter by chapter.",
    features: [
      "Chapter-by-chapter guidance",
      "Personalized coaching sessions",
      "Book development support",
      "Publishing roadmap",
      "Direct access to Brown Patience",
    ],
    price: "₦40,000",
    billingPeriod: "/month",
  },
];

const Subscription = () => {
  const [currentIndex, setCurrentIndex] = useState(0); // For mobile carousel
  const [desktopCurrentIndex, setDesktopCurrentIndex] = useState(0); // For desktop carousel
  const [isMobile, setIsMobile] = useState(false);

  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const sectionSpring = useSpring({
    opacity: inView ? 1 : 0,
    transform: inView ? "translateY(0px)" : "translateY(50px)",
  });

  // Check if mobile view
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024); // lg breakpoint
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  // Auto-advance carousel on mobile
  useEffect(() => {
    if (!isMobile) return;

    const timer = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % subscriptionItems.length);
    }, 8000); // Change every 8 seconds

    return () => clearInterval(timer);
  }, [isMobile]);

  // Auto-advance carousel on desktop
  useEffect(() => {
    if (isMobile) return;

    const timer = setInterval(() => {
      setDesktopCurrentIndex((prev) => (prev + 1) % subscriptionItems.length);
    }, 10000); // Change every 10 seconds (slower than mobile)

    return () => clearInterval(timer);
  }, [isMobile]);

  // Mobile carousel navigation functions
  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % subscriptionItems.length);
  };

  const prevSlide = () => {
    setCurrentIndex(
      (prev) => (prev - 1 + subscriptionItems.length) % subscriptionItems.length
    );
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  // Desktop carousel navigation functions
  const nextDesktopSlide = () => {
    setDesktopCurrentIndex((prev) => (prev + 1) % subscriptionItems.length);
  };

  const prevDesktopSlide = () => {
    setDesktopCurrentIndex(
      (prev) => (prev - 1 + subscriptionItems.length) % subscriptionItems.length
    );
  };

  const goToDesktopSlide = (index: number) => {
    setDesktopCurrentIndex(index);
  };

  const handleGetBook = (bookId: string, title: string) => {
    // Analytics tracking would go here
    console.log("Book subscription tracked:", { bookId, title });

    // For "Newcomer to Book Writing", redirect to specific link
    if (bookId === "newcomer") {
      window.open("https://selar.com/NewcomerToBookWriting", "_blank");
      return;
    }

    // For other subscriptions, open WhatsApp with custom message
    let message = "";
    switch (bookId) {
      case "art-of-writing":
        message = `Hi Patience! I'm interested in "The Art of Writing Compelling Stories" subscription. Could you please provide more details?`;
        break;
      case "solopreneurs":
        message = `Hi Patience! I'm interested in the "Guidance for Writing Solopreneurs" subscription. Could you please provide more details?`;
        break;
      case "coaching-authors":
        message = `Hi Patience! I'm interested in the "Coaching For Authors" subscription. Could you please provide more details?`;
        break;
      default:
        message = `Hi Patience! I'm interested in the "${title}" subscription. Could you please provide more details?`;
    }

    const whatsappUrl = `https://wa.me/2348140170221?text=${encodeURIComponent(
      message
    )}`;
    window.open(whatsappUrl, "_blank");
  };

  return (
    <section id="subscriptions" ref={ref} className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <animated.div style={sectionSpring}>
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium mb-4">
              <BookOpen className="w-4 h-4" />
              Subscriptions
            </div>
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
              Transform Your Writing Journey
            </h2>
            <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
              Discover comprehensive resources and personalized guidance to help
              you become the writer you've always wanted to be.
            </p>
          </div>

          {/* Desktop Carousel Layout */}
          <div className="hidden lg:block mb-16">
            <div className="relative overflow-hidden rounded-2xl">
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{
                  transform: `translateX(-${desktopCurrentIndex * 100}%)`,
                }}
              >
                {subscriptionItems.map((book, index) => (
                  <div key={book.id} className="w-full flex-shrink-0 px-2">
                    <animated.div
                      style={{
                        opacity: inView ? 1 : 0,
                        transform: inView
                          ? "translateY(0px)"
                          : "translateY(50px)",
                        transitionDelay: inView
                          ? `${300 + index * 150}ms`
                          : "0ms",
                        transition: "all 0.6s ease-out",
                      }}
                    >
                      <Card
                        className={`group h-full hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1 interactive-lift overflow-hidden ${
                          book.popular ? "ring-2 ring-brand-accent/20" : ""
                        }`}
                        style={{
                          minHeight: "clamp(400px, 50vh, 500px)",
                        }}
                      >
                        <CardContent className="p-0 h-full">
                          <div className="flex h-fit">
                            {/* Book Cover */}
                            <div className="w-2/5 min-h-[300px]">
                              <img
                                src={book.cover}
                                alt={`Cover of ${book.title}`}
                                className="w-full h-full object-cover object-center group-hover:scale-105 transition-transform duration-500"
                                loading="lazy"
                              />
                            </div>

                            {/* Content */}
                            <div className="w-3/5 p-6 flex flex-col justify-between">
                              <div>
                                {book.popular && (
                                  <div className="inline-block bg-brand-accent text-brand-primary text-xs px-2 py-1 rounded-full mb-3">
                                    Most Popular
                                  </div>
                                )}

                                <h3 className="text-xl font-serif font-bold text-brand-secondary mb-3 leading-tight">
                                  {book.title}
                                </h3>

                                <p className="text-brand-secondary/80 leading-relaxed mb-4 text-sm">
                                  {book.description}
                                </p>

                                {/* Features */}
                                <div className="space-y-2 mb-4">
                                  {book.features
                                    .slice(0, 3)
                                    .map((feature, idx) => (
                                      <div
                                        key={idx}
                                        className="flex items-start gap-2"
                                      >
                                        <Check className="w-3 h-3 text-brand-accent flex-shrink-0 mt-0.5" />
                                        <span className="text-brand-secondary/70 text-xs leading-relaxed">
                                          {feature}
                                        </span>
                                      </div>
                                    ))}
                                </div>
                              </div>

                              {/* Price and CTA */}
                              <div>
                                <div className="flex items-end gap-1 mb-4">
                                  <span className="text-2xl font-bold text-brand-secondary">
                                    {book.price}
                                  </span>
                                  <span className="text-brand-secondary/60 text-sm">
                                    {book.billingPeriod}
                                  </span>
                                </div>

                                <Button
                                  variant={book.popular ? "hero" : "outline"}
                                  className="w-full group/btn"
                                  onClick={() =>
                                    handleGetBook(book.id, book.title)
                                  }
                                >
                                  {book.id === "newcomer"
                                    ? "Buy Now"
                                    : "Contact Me"}
                                  <ArrowRight className="w-4 h-4 ml-1 group-hover/btn:translate-x-1 transition-transform" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </animated.div>
                  </div>
                ))}
              </div>
            </div>

            {/* Desktop Carousel Navigation */}
            <div className="flex items-center justify-center gap-4 mt-6">
              <Button
                variant="ghost"
                size="icon"
                onClick={prevDesktopSlide}
                className="rounded-full hover:bg-brand-accent/10 h-10 w-10"
              >
                <ChevronLeft className="w-5 h-5" />
              </Button>

              {/* Dots */}
              <div className="flex gap-2">
                {subscriptionItems.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => goToDesktopSlide(index)}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      index === desktopCurrentIndex
                        ? "bg-brand-accent scale-110"
                        : "bg-brand-grey hover:bg-brand-accent/50"
                    }`}
                  />
                ))}
              </div>

              <Button
                variant="ghost"
                size="icon"
                onClick={nextDesktopSlide}
                className="rounded-full hover:bg-brand-accent/10 h-10 w-10"
              >
                <ChevronRight className="w-5 h-5" />
              </Button>
            </div>

            {/* Desktop Progress Indicator */}
            <div className="text-center mt-3">
              <div className="text-sm text-brand-secondary/60">
                {desktopCurrentIndex + 1} of {subscriptionItems.length}
              </div>
            </div>
          </div>

          {/* Mobile Carousel */}
          <div className="lg:hidden mb-16">
            <div className="relative overflow-hidden rounded-2xl">
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentIndex * 100}%)` }}
              >
                {subscriptionItems.map((book) => (
                  <div key={book.id} className="w-full flex-shrink-0 px-2">
                    <Card
                      className={`group h-full hover:shadow-elegant transition-all duration-300 interactive-lift mx-auto max-w-sm overflow-hidden ${
                        book.popular ? "ring-2 ring-brand-accent/20" : ""
                      }`}
                      style={{
                        minHeight: "clamp(600px, 80vh, 750px)",
                      }}
                    >
                      <CardContent className="p-0 h-full">
                        <div className="flex flex-col h-full">
                          {/* Book Cover */}
                          <div className="w-full aspect-[3/4] min-h-[250px]">
                            <img
                              src={book.cover}
                              alt={`Cover of ${book.title}`}
                              className="w-full h-full object-cover object-center group-hover:scale-105 transition-transform duration-500"
                              loading="lazy"
                            />
                          </div>

                          {/* Content */}
                          <div className="p-4 flex flex-col justify-between flex-1">
                            <div>
                              {book.popular && (
                                <div className="inline-block bg-brand-accent text-brand-primary text-xs px-2 py-1 rounded-full mb-3">
                                  Most Popular
                                </div>
                              )}

                              <h3 className="text-lg font-serif font-bold text-brand-secondary mb-3 leading-tight">
                                {book.title}
                              </h3>

                              <p className="text-brand-secondary/80 leading-relaxed mb-4 text-sm">
                                {book.description}
                              </p>

                              {/* Features */}
                              <div className="space-y-2 mb-4">
                                {book.features
                                  .slice(0, 4)
                                  .map((feature, idx) => (
                                    <div
                                      key={idx}
                                      className="flex items-start gap-2"
                                    >
                                      <Check className="w-3 h-3 text-brand-accent flex-shrink-0 mt-0.5" />
                                      <span className="text-brand-secondary/70 text-xs leading-relaxed">
                                        {feature}
                                      </span>
                                    </div>
                                  ))}
                              </div>
                            </div>

                            {/* Price and CTA */}
                            <div className="mt-auto">
                              <div className="flex items-end gap-1 mb-4">
                                <span className="text-xl font-bold text-brand-secondary">
                                  {book.price}
                                </span>
                                <span className="text-brand-secondary/60 text-sm">
                                  {book.billingPeriod}
                                </span>
                              </div>

                              <Button
                                variant={book.popular ? "hero" : "outline"}
                                className="w-full group/btn"
                                onClick={() =>
                                  handleGetBook(book.id, book.title)
                                }
                              >
                                {book.id === "newcomer"
                                  ? "Buy Now"
                                  : "Contact Me"}
                                <ArrowRight className="w-4 h-4 ml-1 group-hover/btn:translate-x-1 transition-transform" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                ))}
              </div>
            </div>

            {/* Mobile Carousel Navigation */}
            <div className="flex items-center justify-center gap-4 mt-6">
              <Button
                variant="ghost"
                size="icon"
                onClick={prevSlide}
                className="rounded-full hover:bg-brand-accent/10 h-8 w-8"
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>

              {/* Dots */}
              <div className="flex gap-1">
                {subscriptionItems.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => goToSlide(index)}
                    className={`w-2 h-2 rounded-full transition-all duration-300 ${
                      index === currentIndex
                        ? "bg-brand-accent scale-110"
                        : "bg-brand-grey hover:bg-brand-accent/50"
                    }`}
                  />
                ))}
              </div>

              <Button
                variant="ghost"
                size="icon"
                onClick={nextSlide}
                className="rounded-full hover:bg-brand-accent/10 h-8 w-8"
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>

            {/* Progress Indicator */}
            <div className="text-center mt-3">
              <div className="text-sm text-brand-secondary/60">
                {currentIndex + 1} of {subscriptionItems.length}
              </div>
            </div>
          </div>

          {/* Call to Action Section */}
          {/* <div className="text-center">
            <div className="bg-brand-neutral/50 rounded-2xl p-8 max-w-2xl mx-auto">
              <div className="flex items-center justify-center gap-2 mb-4">
                <div className="w-8 h-8 bg-brand-accent/10 rounded-full flex items-center justify-center">
                  <BookOpen className="w-5 h-5 text-brand-accent" />
                </div>
                <h3 className="text-lg font-serif font-bold text-brand-secondary">
                  Start Your Writing Journey Today
                </h3>
              </div>
              <p className="text-brand-secondary/70 mb-6">
                Choose the perfect resource to transform your writing and bring
                your stories to life.
              </p>
              <Button
                variant="hero"
                size="lg"
                onClick={() =>
                  document
                    .getElementById("subscription")
                    ?.scrollIntoView({ behavior: "smooth" })
                }
                className="group"
              >
                Explore All Options
                <ArrowRight className="w-5 h-5 ml-1 group-hover:translate-x-1 transition-transform" />
              </Button>
            </div>
          </div> */}
        </animated.div>
      </div>
    </section>
  );
};

export default Subscription;
