import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Mail, MessageCircle, Eye } from "lucide-react";
import footerlogo from "@/assets/footerLogo.webp";

const Footer = () => {
  const [reducedMotion, setReducedMotion] = useState(false);

  useEffect(() => {
    // Check if user has reduced motion preference
    const hasReducedMotion = window.matchMedia(
      "(prefers-reduced-motion: reduce)"
    ).matches;
    const storedPreference = localStorage.getItem("reducedMotion");
    setReducedMotion(storedPreference === "true" || hasReducedMotion);
  }, []);

  const toggleReducedMotion = () => {
    const newValue = !reducedMotion;
    setReducedMotion(newValue);
    localStorage.setItem("reducedMotion", String(newValue));

    // Add or remove class from document root
    if (newValue) {
      document.documentElement.classList.add("reduced-motion");
    } else {
      document.documentElement.classList.remove("reduced-motion");
    }
  };

  const scrollToNewsletter = () => {
    document
      .getElementById("newsletter")
      ?.scrollIntoView({ behavior: "smooth" });
  };

  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-brand-secondary text-brand-primary py-16">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {/* Brand & Newsletter */}
          <div className="lg:col-span-2 space-y-6">
            <div className="flex items-center gap-2">
              {/* Desktop/Tablet Logo */}
              <img
                src={footerlogo}
                alt="Author Logo"
                className="hidden sm:block h-12 w-auto"
              />
              {/* Mobile Logo */}
              <img
                src={footerlogo}
                alt="Author Logo"
                className="sm:hidden h-12 w-auto"
              />
            </div>

            <p className="text-brand-primary/80 leading-relaxed max-w-md">
              Empowering writers to find their voice, craft compelling stories,
              and navigate the publishing world with confidence and clarity.
            </p>

            <div className="space-y-3">
              <h4 className="font-semibold text-brand-primary">
                Never Miss a Writing Tip
              </h4>
              <Button
                onClick={scrollToNewsletter}
                variant="outline"
                className="border-brand-primary text-brand-primary hover:bg-brand-primary hover:text-brand-secondary"
              >
                <Mail className="w-4 h-4 mr-2" />
                Join Newsletter
              </Button>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="font-semibold text-brand-primary mb-4">
              Quick Links
            </h4>
            <nav className="space-y-2">
              <a
                href="home"
                className="block text-brand-primary/80 hover:text-brand-accent transition-colors"
              >
                Home
              </a>
              <a
                href="/about"
                className="block text-brand-primary/80 hover:text-brand-accent transition-colors"
              >
                About
              </a>
              <a
                href="/books"
                className="block text-brand-primary/80 hover:text-brand-accent transition-colors"
              >
                Books
              </a>
              <a
                href="/blog"
                className="block text-brand-primary/80 hover:text-brand-accent transition-colors"
              >
                Blog
              </a>
              {/* <a
                href="/contact"
                className="block text-brand-primary/80 hover:text-brand-accent transition-colors"
              >
                Contact
              </a> */}
            </nav>
          </div>

          {/* Services & Legal */}
          <div>
            <h4 className="font-semibold text-brand-primary mb-4">Services</h4>
            <nav className="space-y-2">
              <a
                href="/services/coaching-for-writers"
                className="block text-brand-primary/80 hover:text-brand-accent transition-colors"
              >
                Writing Coaching
              </a>
              <a
                href="/services/book-writing-editing"
                className="block text-brand-primary/80 hover:text-brand-accent transition-colors"
              >
                Book Writing & Editing
              </a>
              <a
                href="/services/content-writing"
                className="block text-brand-primary/80 hover:text-brand-accent transition-colors"
              >
                Content Writing
              </a>
              <a
                href="/community"
                className="block text-brand-primary/80 hover:text-brand-accent transition-colors"
              >
                Writer's Community
              </a>
            </nav>

            <div className="mt-8">
              <h5 className="font-medium text-brand-primary mb-2">Legal</h5>
              <a
                href="/privacy"
                className="block text-brand-primary/60 hover:text-brand-accent transition-colors text-sm"
              >
                Privacy Policy
              </a>
              <a
                href="/terms"
                className="block text-brand-primary/60 hover:text-brand-accent transition-colors text-sm"
              >
                Terms of Service
              </a>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-brand-primary/20 pt-8">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
            <div className="flex items-center gap-4 text-sm text-brand-primary/60">
              <span>
                © {currentYear} The Brown Patience Company. All rights reserved.
              </span>
              <span className="hidden sm:block">•</span>
              <span>Built with ❤️ for writers</span>
            </div>

            {/* Accessibility Controls */}
            <div className="flex items-center gap-4">
              <Button
                onClick={toggleReducedMotion}
                variant="ghost"
                size="sm"
                className="text-brand-primary/80 hover:text-brand-accent h-8 px-3"
              >
                <Eye className="w-4 h-4 mr-1" />
                {reducedMotion ? "Enable" : "Reduce"} Motion
              </Button>

              <div className="text-xs text-brand-primary/40">
                Last updated: Jan 2024
              </div>
            </div>
          </div>
        </div>

        {/* Contact CTA */}
        <div className="mt-12 text-center">
          <div className="inline-flex items-center gap-4 bg-white rounded-2xl p-6">
            <MessageCircle className="w-6 h-6 text-brand-accent flex-shrink-0" />
            <div className="text-left">
              <div className="font-medium text-brand-secondary">
                Ready to start your writing journey?
              </div>
              <div className="text-sm text-brand-secondary/70">
                Get in touch for personalized guidance
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="border-brand-accent text-brand-accent hover:bg-brand-accent hover:text-brand-primary"
              onClick={() =>
                document
                  .getElementById("contact")
                  ?.scrollIntoView({ behavior: "smooth" })
              }
            >
              Let's Chat
            </Button>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
