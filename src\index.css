@import url("https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,600;0,700;1,400&family=Inter:wght@300;400;500;600;700&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Author Homepage Design System
Warm, professional, cozy brand with polished micro-interactions
*/

@layer base {
  :root {
    /* Brand Colors - Warm, Professional, Cozy */
    --brand-primary: 0 0% 100%; /* #ffffff */
    --brand-secondary: 0 0% 23%; /* #3a3a3a */
    --brand-accent: 0 94% 49%; /* #ef0909 */
    --brand-accent-light: 0 94% 49% / 0.42; /* #ef09096b */
    --brand-neutral: 0 0% 93%; /* #eeee */
    --brand-grey: 0 0% 88%; /* #E1E1E1 */
    --brand-black: 0 0% 7%; /* #111 */

    /* Semantic Color System */
    --background: var(--brand-primary);
    --foreground: var(--brand-secondary);

    --card: var(--brand-primary);
    --card-foreground: var(--brand-secondary);

    --popover: var(--brand-primary);
    --popover-foreground: var(--brand-secondary);

    --primary: var(--brand-accent);
    --primary-foreground: var(--brand-primary);

    --secondary: var(--brand-neutral);
    --secondary-foreground: var(--brand-secondary);

    --muted: var(--brand-grey);
    --muted-foreground: var(--brand-secondary);

    --accent: var(--brand-accent);
    --accent-foreground: var(--brand-primary);

    --destructive: 0 84% 60%;
    --destructive-foreground: var(--brand-primary);

    --border: var(--brand-grey);
    --input: var(--brand-grey);
    --ring: var(--brand-accent);

    /* Author-specific variants */
    --hero-gradient: linear-gradient(
      135deg,
      hsl(var(--brand-primary)),
      hsl(var(--brand-neutral))
    );
    --hero-accent-gradient: linear-gradient(
      135deg,
      hsl(var(--brand-primary)),
      hsl(var(--brand-accent) / 0.15)
    );
    --oasis-gradient: linear-gradient(
      135deg,
      hsl(120 60% 85%),
      hsl(180 40% 70%)
    );
    --accent-gradient: linear-gradient(
      135deg,
      hsl(var(--brand-accent)),
      hsl(0 94% 45%)
    );
    --warm-shadow: 0 10px 30px -5px hsl(var(--brand-accent) / 0.1);
    --elegant-shadow: 0 4px 20px -4px hsl(var(--brand-secondary) / 0.15);
    --glass-bg: hsl(var(--brand-primary) / 0.8);

    /* Typography Scale */
    --font-serif: "Playfair Display", Georgia, serif;
    --font-sans: "Inter", system-ui, sans-serif;

    /* Animation & Transitions */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-spring: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;

  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;

  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;

  --primary: 210 40% 98%;
  --primary-foreground: 222.2 47.4% 11.2%;

  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;

  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;

  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;

  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;

  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 212.7 26.8% 83.9%;
  --sidebar-background: 240 5.9% 10%;
  --sidebar-foreground: 240 4.8% 95.9%;
  --sidebar-primary: 224.3 76.3% 48%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 240 3.7% 15.9%;
  --sidebar-accent-foreground: 240 4.8% 95.9%;
  --sidebar-border: 240 3.7% 15.9%;
  --sidebar-ring: 217.2 91.2% 59.8%;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-family: var(--font-sans);
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: var(--font-serif);
  }
}

@layer components {
  /* Glass morphism header */
  .header-glass {
    @apply backdrop-blur-md bg-[var(--glass-bg)] border-b border-border/20;
  }

  /* Hero gradient background */
  .hero-bg {
    background: var(--hero-gradient);
  }

  /* Hero with accent gradient background */
  .hero-accent-bg {
    background: var(--hero-accent-gradient);
  }

  /* Oasis gradient background for Community page */
  .oasis-bg {
    background: var(--oasis-gradient);
  }

  /* Elegant shadows */
  .shadow-warm {
    box-shadow: var(--warm-shadow);
  }

  .shadow-elegant {
    box-shadow: var(--elegant-shadow);
  }

  /* Typography variants */
  .text-serif {
    font-family: var(--font-serif);
  }

  .text-sans {
    font-family: var(--font-sans);
  }

  /* Interactive elements */
  .interactive-lift {
    @apply transition-transform duration-300 hover:scale-[1.02] hover:-translate-y-1;
  }

  .interactive-glow:hover {
    box-shadow: 0 0 30px hsl(var(--brand-accent) / 0.3);
  }

  /* Reduced motion support */
  .reduced-motion * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

@layer utilities {
  /* Custom animations */
  .animate-fade-up {
    animation: fadeUp 0.6s ease-out forwards;
  }

  .animate-slide-in {
    animation: slideIn 0.8s ease-out forwards;
  }

  .animate-scale-in {
    animation: scaleIn 0.5s ease-out forwards;
  }

  /* Staggered animations */
  .stagger-1 {
    animation-delay: 0.1s;
  }
  .stagger-2 {
    animation-delay: 0.2s;
  }
  .stagger-3 {
    animation-delay: 0.3s;
  }
  .stagger-4 {
    animation-delay: 0.4s;
  }

  /* Typewriter cursor animation */
  .typewriter-cursor {
    display: inline-block;
    width: 3px;
    height: 1em;
    background-color: currentColor;
    margin-left: 4px;
    animation: blink 1s infinite;
    vertical-align: baseline;
  }
}

@keyframes fadeUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Typewriter cursor blink animation */
@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
